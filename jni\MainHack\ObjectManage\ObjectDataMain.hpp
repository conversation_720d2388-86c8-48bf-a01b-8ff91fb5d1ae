#ifndef OBJECTDATAMAIN_HPP
#define OBJECTDATAMAIN_HPP

#include <iostream>
#include <vector>
#include <cstring>
#include "DataStruct.h"
#include "diRW/kernelRW.h"
#include "Other/BoneGet.hpp"

using namespace std;


class OffsetData
{
public:
    int Health_offset = 0xed8;
    int MaxHealth_offset = 0xee0;
    int Component_offset = 0x278;
    int RelativeLocation_offset = 0x200;
    int Team_offset = 0xac0;
    int State_offset = 0x1538;
    int Filter_offset = 0x3518;
    int Name_offset = 0xa40;
    int Ai_offset = 0xadc;
    int Bot_offset = 0xadc;
    int Mesh_offset = 0x600;
    int HuMan_offset = 0x1f0;
    int Bone_offset = 0x7f8;
    int Fire_offset = 0x1688;
    int Scope_offset = 0x1069;
    int WeaponManage_offset = 0x2438;
    int CurrentWeapon_offset = 0x558;
    int WeaponEntityComp_offset = 0x840;
    int HandGun_offset = 0xbd0;
};

enum ItemType {
    SUPPLY = 0,
    VEHICLE = 1
};

class ObjectData
{
public:
    ObjectData(
    uintptr_t addr, Vector3D location, int health, int healthMax, int teamID, int state, const char *name);
    ObjectData(uintptr_t addr);
    ObjectData();
    
    //check func
    int getFindCount();
    void addFindCount();
    void setFindCountZero();

    // data
    uintptr_t ObjectAddr;
    Vector3D RelativeLocation;
    int Health;
    int HealthMax;
    int TeamID;
    int State;
    int IsBot;
    int Fire;
    int Scope;
    int HandGun;
    char PlayerName[32];
    Vector3D BoneRelativeLocation[17];

    
    //check
    int FindCount;
    
};


class ItemData
{
public:
    ItemData(uintptr_t addr, const char* classname, const char *name, ItemType type);
    ItemData(uintptr_t addr);
    ItemData();
    
    	//check func
    int getFindCount();
    void addFindCount();
    void setFindCountZero();

    	// data
    uintptr_t ObjectAddr;
    ItemType Type;
    Vector3D RelativeLocation;
    char ClassName[128];
    char Name[32];
    
        //matched data
    int distance;
    Vector4D ScreenCoord;
      
    	//check data
    int FindCount;
    
};

class ObjectDataManager
{
public:
    // rw
    mainRW *rw;
    OffsetData offsetData;

    // init
    ObjectDataManager();
    void InitObjectDataManager(int pid);
    bool IsInit();

    // set
    void addObjectData(uintptr_t addr);
    void removeObjectData(uintptr_t addr);
    void cleanObjectData();
    void upDateObjectData(bool IsAll);
    inline void upDateObjectData(ObjectData* object, bool IsAll);
    
    void addItemData(uintptr_t addr, const char* classname, const char* name, ItemType type);
    void removeItemData(uintptr_t addr);
    void cleanItemData();
    void upDateItemData(bool IsAll);
    inline void upDateItemData(ItemData* object, bool IsAll);
    
    void setSelfData(uintptr_t addr);
    void upDateSelfData();

    

    // get
    ObjectData *getObjectData(int index);
    ObjectData *getObjectData(uintptr_t addr);
    ItemData *getItemData(int index);
    ItemData *getItemData(uintptr_t addr);
    ObjectData *getSelfData();
    std::vector<ObjectData> *getObjectData();
    std::vector<ItemData> *getItemData();

private:
    // data
    int pid;
    bool isInit = false;
    BoneGet boneGet;
    std::vector<ObjectData> ObjectDataList;
    std::vector<ItemData> ItemDataList;
    ObjectData SelfData;

    // find
    std::vector<ObjectData>::iterator findObjectDataIterator(uintptr_t addr);
    std::vector<ItemData>::iterator findItemDataIterator(uintptr_t addr);
};

#endif