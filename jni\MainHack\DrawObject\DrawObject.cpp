#include "DrawObject.hpp"
#include "绘图.h"
#include "计算.h"

绘图 draw;

void initDraw(float Px, float Py)
{
	draw.初始化绘图(Px, Py);
}

void DrawObject(std::vector<ObjectData> *objectData, ObjectData *selfData, float matrix[16])
{
	if (objectData == nullptr)
		return;

	int sum = objectData->size();
	draw.绘制人数(sum);
	for (int i = 0; i < sum; i++)
	{
		ObjectData *Object = &((*objectData)[i]);
		
		if (Object->TeamID == selfData->TeamID)
			continue;
		Vector4D 屏幕坐标 = 计算屏幕坐标(matrix, Object->RelativeLocation, draw.px, draw.py);
		if (屏幕坐标.W > 0)
		{
			draw.绘制方框(屏幕坐标, Object->IsBot);
			draw.绘制血量(屏幕坐标, Object->HealthMax, Object->Health, Object->IsBot);
			draw.绘制射线(屏幕坐标, Object->IsBot);
			draw.绘制距离(屏幕坐标, 计算距离(selfData->RelativeLocation, Object->RelativeLocation));
			draw.绘制名字(屏幕坐标, Object->TeamID, Object->PlayerName, Object->IsBot);
			draw.绘制骨骼(计算骨骼(matrix, Object->BoneRelativeLocation, draw.px, draw.py), 屏幕坐标);
		}
	}
}


void DrawItem(std::vector<ItemData> *itemData, ObjectData *selfData, float matrix[16]){
	if (itemData == nullptr)
		return;

	int sum = itemData->size();
	for (int i = 0; i < sum; i++)
	{
		ItemData *Object = &((*itemData)[i]);
		Vector4D 屏幕坐标 = 计算屏幕坐标(matrix, Object->RelativeLocation, draw.px, draw.py);
			if (屏幕坐标.W > 0)
		{
		draw.绘制物资(屏幕坐标, 计算距离(selfData->RelativeLocation, Object->RelativeLocation), Object->Name);
		}
	}
}