#include "ObjectDataMain.hpp"



// init
ObjectDataManager::ObjectDataManager(){
ObjectDataList.reserve(40);
ItemDataList.reserve(400);
}


void ObjectDataManager::InitObjectDataManager(int Pid)
{
	this->pid = Pid;
	rw = new kernelRW(Pid); // 初始化读写
	boneGet.initRW(rw);
	this->isInit = true;
}

bool ObjectDataManager::IsInit()
{
	return this->isInit;
}

// set
void ObjectDataManager::addObjectData(uintptr_t addr)
{
	auto DataIterator = findObjectDataIterator(addr);
	if (DataIterator != ObjectDataList.end()){
		DataIterator->addFindCount();
		return;//数据已存在
	}
	ObjectData newData(addr);
	upDateObjectData(&newData,true);
	ObjectDataList.push_back(newData);
}

void ObjectDataManager::removeObjectData(uintptr_t addr)
{
	auto vec = findObjectDataIterator(addr);
	if (vec != ObjectDataList.end())
	{
		ObjectDataList.erase(vec);
	}
}

void ObjectDataManager::cleanObjectData(){
	//clean object
	for (int i = 0; i < ObjectDataList.size(); i++)
	{
		ObjectData *object = getObjectData(i);
		if(object==nullptr)
			continue;
		if(object->FindCount <= 0){
			removeObjectData(object->ObjectAddr);
			i--;
			continue;
			}
		object->setFindCountZero();
	}
}


void ObjectDataManager::upDateObjectData(bool IsAll)
{
	// updata object
	for (int i = 0; i < ObjectDataList.size(); i++)
	{
		ObjectData *object = &ObjectDataList[i];
		upDateObjectData(object,IsAll);
	}
}

inline void ObjectDataManager::upDateObjectData(ObjectData* object, bool IsAll)
{
	uintptr_t ObjectAddr = object->ObjectAddr; // 对象地址
	
	//不需要实时更新的数据
		if(IsAll){
			object->HealthMax = rw->getFloat(ObjectAddr + offsetData.MaxHealth_offset);
			//人机判断
			object->IsBot = rw->getBool(ObjectAddr + offsetData.Bot_offset);
			object->TeamID = rw->getDword(ObjectAddr + offsetData.Team_offset);
			strcpy(object->PlayerName, rw->getUTF8(rw->getPtr64(ObjectAddr + offsetData.Name_offset)));
			}
	
			uintptr_t Locat_addr = rw->getPtr64(ObjectAddr + offsetData.Component_offset);
			rw->readv(Locat_addr + offsetData.RelativeLocation_offset, &object->RelativeLocation, sizeof(Vector3D));
			object->Health = rw->getFloat(ObjectAddr + offsetData.Health_offset);
			object->State = rw->getDword(ObjectAddr + offsetData.State_offset);
			uintptr_t MeshAddr = rw->getPtr64(ObjectAddr + offsetData.Mesh_offset);
			boneGet.更新骨骼数据(MeshAddr + offsetData.HuMan_offset, rw->getPtr64(MeshAddr + offsetData.Bone_offset) + 0x30, object->BoneRelativeLocation);
}


void ObjectDataManager::addItemData(uintptr_t addr, const char* classname, const char* name, ItemType type)
{
	auto DataIterator = findItemDataIterator(addr);
	if (DataIterator != ItemDataList.end()){
		DataIterator->addFindCount();
		return;//数据已存在
	}
	ItemData newData(addr,classname,name,type);
	upDateItemData(&newData,true);
	ItemDataList.push_back(newData);
}

void ObjectDataManager::removeItemData(uintptr_t addr)
{
	auto vec = findItemDataIterator(addr);
	if (vec != ItemDataList.end())
	{
		ItemDataList.erase(vec);
	}
}


void ObjectDataManager::cleanItemData(){
	//clean object
	for (int i = 0; i < ItemDataList.size(); i++)
	{
		ItemData *object = getItemData(i);
		if(object==nullptr)
			continue;
		if(object->FindCount <= 0){
			removeItemData(object->ObjectAddr);
			i--;
			continue;
			}
		object->setFindCountZero();
	}
}

void ObjectDataManager::upDateItemData(bool IsAll)
{
	// updata object
	for (int i = 0; i < ItemDataList.size(); i++)
	{
		ItemData *object = &ItemDataList[i];
		if(IsAll){
		upDateItemData(object,IsAll);
		}else{
		upDateItemData(object,(object->Type==VEHICLE));
		}
	}
}

inline void ObjectDataManager::upDateItemData(ItemData* object, bool IsAll)
{
	uintptr_t ObjectAddr = object->ObjectAddr; // 对象地址	
	//不需要实时更新的数据
		if(IsAll){
			uintptr_t Locat_addr = rw->getPtr64(ObjectAddr + offsetData.Component_offset);
			rw->readv(Locat_addr + offsetData.RelativeLocation_offset, &object->RelativeLocation, sizeof(Vector3D));
			}
}


void ObjectDataManager::setSelfData(uintptr_t addr)
{
	SelfData.ObjectAddr = addr;
}

void ObjectDataManager::upDateSelfData()
{
	uintptr_t ObjectAddr = SelfData.ObjectAddr;			   // 对象地址
	ObjectData *object = &SelfData; // 结构指针
	uintptr_t Locat_addr = rw->getPtr64(ObjectAddr + offsetData.Component_offset);
	rw->readv(Locat_addr + offsetData.RelativeLocation_offset, &object->RelativeLocation, sizeof(Vector3D)); // 坐标
	object->Health = rw->getFloat(ObjectAddr + offsetData.Health_offset);
	object->HealthMax = rw->getFloat(ObjectAddr + offsetData.MaxHealth_offset);
	object->TeamID = rw->getDword(ObjectAddr + offsetData.Team_offset);
	object->State = rw->getDword(ObjectAddr + offsetData.State_offset);
	object->Fire = rw->getDword(ObjectAddr + offsetData.Fire_offset);
	object->Scope = rw->getDword(ObjectAddr + offsetData.Scope_offset);
	uintptr_t WeaponManage_addr = rw->getPtr64(ObjectAddr + offsetData.WeaponManage_offset);
	uintptr_t CurrentWeapon_addr = rw->getPtr64(WeaponManage_addr + offsetData.CurrentWeapon_offset);
	uintptr_t WeaponEntityComp_addr = rw->getPtr64(CurrentWeapon_addr + offsetData.WeaponEntityComp_offset);
	object->HandGun = rw->getDword(WeaponEntityComp_addr + offsetData.HandGun_offset);
}



// get
ObjectData *ObjectDataManager::getObjectData(int index)
{
	if (index >= 0 && index < ObjectDataList.size())
	{
		return &ObjectDataList[index];
	}
	return nullptr;
}

ObjectData *ObjectDataManager::getObjectData(uintptr_t addr)
{
	auto vec = findObjectDataIterator(addr);
	if (vec != ObjectDataList.end())
	{
		return &(*vec);
	}
	return nullptr;
}

ObjectData *ObjectDataManager::getSelfData()
{
	return &SelfData;
}

std::vector<ObjectData> *ObjectDataManager::getObjectData()
{
	return &ObjectDataList;
}

// find
std::vector<ObjectData>::iterator ObjectDataManager::findObjectDataIterator(uintptr_t addr)
{
	for (auto vec = ObjectDataList.begin(); vec != ObjectDataList.end(); vec++)
	{
		if (vec->ObjectAddr == addr)
		{
			return vec;
		}
	}
	// not found,return ObjectDataList.end()
	return ObjectDataList.end();
}

ItemData *ObjectDataManager::getItemData(int index)
{
	if (index >= 0 && index < ItemDataList.size())
	{
		return &ItemDataList[index];
	}
	return nullptr;
}

ItemData *ObjectDataManager::getItemData(uintptr_t addr)
{
	auto vec = findItemDataIterator(addr);
	if (vec != ItemDataList.end())
	{
		return &(*vec);
	}
	return nullptr;
}


std::vector<ItemData> *ObjectDataManager::getItemData()
{
	return &ItemDataList;
}

// find
std::vector<ItemData>::iterator ObjectDataManager::findItemDataIterator(uintptr_t addr)
{
	for (auto vec = ItemDataList.begin(); vec != ItemDataList.end(); vec++)
	{
		if (vec->ObjectAddr == addr)
		{
			return vec;
		}
	}
	// not found,return ObjectDataList.end()
	return ItemDataList.end();
}