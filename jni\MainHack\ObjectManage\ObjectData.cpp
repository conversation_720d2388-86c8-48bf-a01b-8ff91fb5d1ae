#include "ObjectDataMain.hpp"

// init
ObjectData::ObjectData(
	uintptr_t addr, Vector3D location, int health, int healthMax, int teamID, int state, const char *name)
{
	this->ObjectAddr = addr;
	this->RelativeLocation = location;
	this->Health = health;
	this->HealthMax = healthMax;
	this->TeamID = teamID;
	this->State = state;
	strcpy(this->PlayerName, name);
	this->FindCount = 1;
}

ObjectData::ObjectData(uintptr_t addr)
{
	this->ObjectAddr = addr;
	this->FindCount = 1;
}

ObjectData::ObjectData() {}


int ObjectData::getFindCount(){
	return this->FindCount;
}

void ObjectData::addFindCount()
{
this->FindCount++;
}

void ObjectData::setFindCountZero()
{
this->FindCount = 0;
}
