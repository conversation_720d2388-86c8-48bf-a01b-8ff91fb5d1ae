#include "main_hack.hpp"
#include "ObjectManage/ObjectDataMain.hpp"
#include "Other/tool.h"
#include "DrawObject/DrawObject.hpp"
#include "Other/classname.hpp"

int MaxCount = 8;

ObjectDataManager objectDataManager;
static mainRW *rw;
uintptr_t libue4;

// 矩阵
float maritx[16];

// 临时数据
int NowCount = 0;
int Count = 0;
uintptr_t Uworld_addr, Maritx_addr, Self_addr, Uleve_addr, Array_addr, Gname_addr = 0;

char *getClassName(uintptr_t addr)
{
	int id = rw->getDword(addr + 0x18);
	long ye = rw->getPtr64(Gname_addr + (id / 0x4000) * 8);
	long xu = rw->getPtr64(ye + (id % 0x4000) * 8);
	static char Item[256];
	rw->readv(xu + 0xc, Item, sizeof(Item));
	return Item;
}

void init_esp(float px, float py)
{
	initDraw(px, py);
	int pid = getPID("com.tencent.tmgp.pubgmhd");
	objectDataManager.InitObjectDataManager(pid);
	rw = objectDataManager.rw;
	libue4 = get_module_base(pid, "libUE4.so");
}

void draw_esp()
{
	if (!objectDataManager.IsInit() || rw == nullptr)
		return;

	Uworld_addr = rw->getPtr64(libue4 + 0x12161358);
	Gname_addr = rw->getPtr64(libue4 + 0x11a98608);
	Maritx_addr = rw->getPtr64(rw->getPtr64(libue4 + 0x12132D60) + 0x20) + 0x270;
	Self_addr = rw->getPtr64(rw->getPtr64(rw->getPtr64(rw->getPtr64(Uworld_addr + 0x98) + 0x88) + 0x30) + 0x32a8);
	Uleve_addr = rw->getPtr64(Uworld_addr + 0x90);
	objectDataManager.setSelfData(Self_addr);

	Array_addr = rw->getPtr64(Uleve_addr + 0xA0);
	Count = rw->getDword(Uleve_addr + 0xA8);
	rw->readv(Maritx_addr, &maritx, sizeof(maritx));

	// 遍历添加人物
	for (int i = 0; i < MaxCount; i++)
	{
		if (NowCount > Count)
		{
			NowCount = 0;
			objectDataManager.cleanObjectData();	  // 清理没用数组
			objectDataManager.cleanItemData();		  // 清理没用数组
			objectDataManager.upDateObjectData(true); // 完整更新人物数组
			objectDataManager.upDateItemData(false);
			break;
		}
		uintptr_t ObjectAddr = rw->getPtr64(Array_addr + 0x8 * NowCount);
		NowCount++;

		if (ObjectAddr < 0xFFFF)
			continue;

		static std::string className;
		className = getClassName(ObjectAddr);

		if (rw->getFloat(ObjectAddr + objectDataManager.offsetData.Filter_offset) == 479.5)
		{
			// 添加人物
			objectDataManager.addObjectData(ObjectAddr);
			continue;
		}
		static std::string itemName;
		itemName = SupplyName(className.c_str());
		if (!itemName.empty())
		{
			// supplys
			objectDataManager.addItemData(ObjectAddr, className.c_str(), itemName.c_str(), SUPPLY);
			continue;
		}
		itemName = VehicleName(className.c_str());
		if (!itemName.empty())
		{
			// Vehicle
			objectDataManager.addItemData(ObjectAddr, className.c_str(), itemName.c_str(), VEHICLE);
			continue;
		}
	}

	// 调试();
	objectDataManager.upDateSelfData();
	objectDataManager.upDateObjectData(false);
	objectDataManager.upDateItemData(false);
	DrawObject(objectDataManager.getObjectData(), objectDataManager.getSelfData(), maritx);
	DrawItem(objectDataManager.getItemData(), objectDataManager.getSelfData(), maritx);
}