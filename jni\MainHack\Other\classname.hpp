struct ItemInfo {
    const char* keyword;
    const char* name;
};

std::string SupplyName(const char* Gname) {
    static const ItemInfo supplys[] = {
        {"Ammo_556mm", "5.56子弹"},
        {"Ammo_762mm", "7.62子弹"},
        {"Ammo_45AC", ".45口径子弹"},
        {"Ammo_9mm", "9毫米子弹"},
        {"Ammo_12Guage", "12口径子弹"},
        {"Ammo_300Magnum", "马格南"},
        {"Ammo_Bolt", "箭矢"},
        {"Ammo_50BMG", "AWR子弹"},
        {"Ammo_57mm", "P90子弹"},
        {"Ammo_Flare", "信号枪子弹"},
        {"Pistol_Flaregun_Wrapper", "信号枪"},
        {"_Pistol_Re", "召回信号枪"},
        {"Ammo_RevivalFlare_Pickup", "召回枪子弹"},
        {"Rifle_AKM_Wrapper", "AKM"},
        {"Rifle_MK47_Wrapper", "MK47"},
        {"Rifle_VAL_Wrapper", "VAL"},
        {"Rifle_M416_Wrapper", "M416"},
        {"Rifle_Famas_Wrapper", "Famas"},
        {"Rifle_M16A4_Wrapper", "M16A4"},
        {"Rifle_SCAR_Wrapper", "SCAR"},
        {"Rifle_QBZ_Wrapper", "QBZ"},
        {"Rifle_G36_Wrapper", "G36C"},
        {"Rifle_M762_Wrapper", "M762"},
        {"Rifle_Groza_Wrapper", "Groza"},
        {"Rifle_AUG_Wrapper", "AUG"},
        {"Other_DP28_Wrapper", "DP28"},
        {"Other_M249_Wrapper", "M249"},
        {"Other_MG36_Wrapper", "MG-36"},
        {"Other_MG3_Wrapper", "MG3"},
        {"Other_PKM_Wrapper", "PKM"},
        {"Rifle_HoneyBadger_Wrapper", "蜜獾"},
        {"MachineGun_UMP9_Wrapper", "UMP-45"},
        {"MachineGun_TommyGun_Wrapper", "汤姆逊冲锋枪"},
        {"MachineGun_PP19_Wrapper", "PP19"},
        {"MachineGun_Uzi_Wrapper", "Uzi"},
        {"MachineGun_Vector_Wrapper", "维克托"},
        {"MachineGun_P90CG17_Wrapper", "P90"},
        {"MachineGun_MP5K_Wrapper", "MP5K"},
        {"Sniper_Kar98k_Wrapper", "Kar98k"},
        {"Sniper_Win94_Wrapper", "Win94"},
        {"Sniper_Mini14_Wrapper", "Mini14"},
        {"Sniper_SKS_Wrapper", "SKS"},
        {"Sniper_M24_Wrapper", "M24"},
        {"Sniper_M200_Wrapper", "M24"},
        {"WEP_Mk14_Pickup", "MK14"},
        {"Sniper_SLR_Wrapper", "SLR射手步枪"},
        {"Sniper_MK12_Wrapper", "MK12射手步枪"},
        {"Sniper_MK20_Wrapper", "MK20-H射手步枪"},
        {"_M417_Wrapper", "M417射手步枪"},
        {"Sniper_VSS_Wrapper", "VSS射手步枪"},
        {"Sniper_QBU_Wrapper", "QBU射手步枪"},
        {"Sniper_AWM_Wrapper", "AWM"},
        {"Sniper_Mosin_Wrapper", "莫辛纳甘"},
        {"Sniper_AMR_Wrapper", "AMR"},
        {"ShotGun_S12K_Wrapper", "S12K"},
        {"ShotGun_DP12_Wrapper", "DBS霰弹枪"},
        {"ShotGun_S686_Wrapper", "S686"},
        {"ShotGun_S1897_Wrapper", "S1897"},
        {"ShotGun_SPAS-12_Wrapper", "SPAS-12"},
        {"ShotGun_AA12_Wrapper", "AA12"},
        {"MZJ_8X_Ballistics_Pickup", "战术瞄准镜"},
        {"MZJ_8X_Pickup", "8倍镜"},
        {"MZJ_6X_Pickup", "6倍镜"},
        {"MZJ_4X_Pickup", "4倍镜"},
        {"MZJ_3X_Pickup", "3倍镜"},
        {"MZJ_2X_Pickup", "2倍镜"},
        {"MZJ_HD_Pickup", "红点瞄准镜"},
        {"MZJ_QX_Pickup", "全息瞄准镜"},
        {"DJ_Large_EQ_Pickup", "步枪快扩"},
        {"DJ_Large_E_Pickup", "步枪扩容"},
        {"DJ_Sniper_EQ_Pickup", "狙击枪快扩"},
        {"DJ_Sniper_E_Pickup", "狙击枪扩容"},
        {"DJ_Mid_E_Pickup", "冲锋枪扩容"},
        {"DJ_Mid_EQ_Pickup", "冲锋枪快扩"},
        {"ckUp_BP_Helmet_Lv1", "一级头"},
        {"ckUp_BP_Armor_Lv1", "一级甲"},
        {"ckUp_BP_Bag_Lv1", "一级包"},
        {"ckUp_BP_Helmet_Lv2", "二级头"},
        {"ckUp_BP_Armor_Lv2", "二级甲"},
        {"ckUp_BP_Bag_Lv2", "二级包"},
        {"ckUp_BP_Helmet_Lv3", "三级头"},
        {"ckUp_BP_Armor_Lv3", "三级甲"},
        {"ckUp_BP_Bag_Lv3", "三级包"},
        {"QK_Large_Suppressor_Pickup", "步枪消音器"},
        {"QK_Sniper_Suppressor_Pickup", "狙击枪消音器"},
        {"QT_Sniper_Pickup", "托腮板"},
        {"ZDD_Sniper_Pickup", "子弹袋"},
        {"QK_Large_Compensator_Pickup", "步枪补偿器"},
        {"QK_Sniper_Compensator_Pickup", "狙击枪补偿器"},
        {"QK_Large_FlashHider_Pickup", "步枪消焰器"},
        {"WB_Vertical_Pickup", "垂直握把"},
        {"QT_A_Pickup", "步枪枪托"},
        {"QT_ZH_Pickup", "撞火枪托"},
        {"QK_DuckBill_Pickup", "鸭嘴枪口"},
        {"WB_Angled_Pickup", "直角握把"},
        {"WB_ThumbGrip_Pickup", "拇指握把"},
        {"WB_LightGrip_Pickup", "轻型握把"},
        {"WB_Lasersight_Pickup", "激光握把"},
        {"WB_HalfGrip_Pickup", "半截式握把"},
        {"jection_Pickup", "肾上腺素"},
        {"rstaid_Pickup", "急救包"},
        {"rstAidbox_Pickup", "医疗箱"},
        {"lls_Pickup", "止痛药"},
        {"ink_Pickup", "能量饮料"},
        {"ndage_Pickup", "绷带"},
        {"Grenade_Shoulei_Weapon_Wrapper", "手榴弹"},
        {"Grenade_Smoke_Weapon_Wrapper", "烟雾弹"},
        {"Grenade_Burn_Weapon_Wrapper", "燃烧瓶"},
        {"Grenade_Weapon_Wrapper_Thermite", "铝热弹"},
        {"CanBattery", "汽油桶"},
        {"Escape_EvacuationPointActor", "撤离点"},
        {"perPeopleSkill", "金插"},
        {"revivalAED_Pickup", "自救器"}
    };
    
	std::string name = "";
    for (const auto& supply : supplys) {
        if (strstr(Gname, supply.keyword) != nullptr) {
           name = supply.name;
            return name;
        }
    }
    return name;
}


std::string VehicleName(const char* Gname) {
static const ItemInfo vehicles[] = {
    {"Mountainbike_Training", "自行车"},
    {"CoupeRB_1", "双人跑车"},
    {"CoupeRB_Base", "双人跑车"},
    {"Scooter", "小绵羊"},
    {"Bigfoot", "大脚车"},
    {"BRDM", "装甲车"},
    {"Motorcycle", "摩托车"},
    {"Tuk", "杜克车"},
    {"ATV", "全地形越野摩托"},
    {"UTV", "农夫车"},
    {"MotorcycleCart", "三轮摩托"},
    {"Snowmobile", "雪地摩托"},
    {"Snowbike", "雪地摩托"},
    {"StationWagon", "旅行车"},
    {"VH_Buggy", "蹦蹦车"},
    {"Dacia", "轿车"},
    {"UAZ", "吉普车"},
    {"Rony", "皮卡车"},
    {"rado_close_1", "四人跑车"},
    {"rado_open_1", "敞篷跑车"},
    {"SportCar", "敞篷跑车"},
    {"Mirado_open", "敞篷跑车"},
    {"Drift_001", "拉力车"},
    {"MiniBus", "迷你巴士"},
    {"PG117", "快艇"},
    {"uaRail_1", "摩托艇"},
    {"Motorglider", "滑翔机"},
    {"icensedTrain", "磁吸小火车"},
    {"TrackVehicle", "摇摇车"},
    {"Horse", "马"}
};
    
	std::string name = "";
    for (const auto& vehicle : vehicles) {
        if (strstr(Gname, vehicle.keyword) != nullptr) {
           name = vehicle.name;
            return name;
        }
    }
    return name;

}