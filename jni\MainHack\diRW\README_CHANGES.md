# diRW 内核读取修改说明

## 修改概述
将 diRW 模块从系统读取方式改为使用 driver.h 的内核读取方式。

## 主要修改内容

### 1. 删除的文件
- `sysRW.cpp` - 系统读取实现文件
- `sysRW.h` - 系统读取头文件

### 2. 修改的文件

#### kernelRW.h
- 移除了对 `<dirent.h>`, `<sys/stat.h>`, `<sys/fcntl.h>`, `<sys/ioctl.h>` 的依赖
- 添加了对 `driver.h` 的引用
- 将 `int fd` 和 `const char *DEVICE_PATH` 成员变量替换为 `Driver *driver`
- 添加了析构函数 `~kernelRW()`

#### kernelRW.cpp
- 删除了所有驱动文件搜索和打开相关的函数：
  - `symbol_file()`
  - `driver_path()`
  - `find_driver_path()`
  - `open_driver()`
- 删除了 ioctl 相关的结构体和枚举：
  - `_COPY_MEMORY` 结构体
  - `_MODULE_BASE` 结构体
  - `OPERATIONS` 枚举
- 重写了构造函数：
  - 创建 Driver 实例
  - 使用 `driver->initpid(pid)` 初始化
  - 添加错误处理
- 添加了析构函数来清理 Driver 实例
- 重写了 `readv()` 和 `writev()` 方法：
  - 使用 `driver->read()` 替代 ioctl 读取
  - 使用 `driver->write()` 替代 ioctl 写入

#### mainRW.h
- 移除了对 `sysRW` 类的友元声明

#### Android.mk
- 添加了对 `libkma_driver.a` 静态库的链接
- 配置了预编译静态库

## 技术优势

### 内核读取的优势
1. **更高的安全性**: 直接在内核层进行内存操作，绕过用户态检测
2. **更好的性能**: 内核层读取效率更高，支持多线程
3. **硬件级支持**: 支持硬件级读取，兼容 QGKI、GKI2.0+
4. **更强的稳定性**: 支持内核版本 4.9~6.6

### Driver 类特性
- 支持 CPU 亲和设置
- 提供安全的进程 PID 获取
- 支持模块地址获取
- 提供模板方法简化使用
- 内置触摸事件支持

## 使用方式
代码的使用方式保持不变，ObjectDataManager 仍然通过以下方式初始化：
```cpp
rw = new kernelRW(pid);
```

所有的读写操作接口保持一致：
- `readv(address, buffer, size)`
- `writev(address, buffer, size)`
- `getFloat()`, `getDword()`, `getBool()` 等便捷方法

## 注意事项
1. 确保设备已安装相应的内核驱动
2. 需要 root 权限才能使用内核读取功能
3. Driver 类使用固定的密钥进行版本验证，确保接口一致性
