#include "ObjectDataMain.hpp"


ItemData::ItemData(uintptr_t addr, const char* classname, const char *name, ItemType type)
{
	this->ObjectAddr = addr;
	this->Type = type;
	strcpy(this->ClassName, classname);
	strcpy(this->Name, name);
	this->FindCount = 1;
}

ItemData::ItemData(uintptr_t addr)
{
	this->ObjectAddr = addr;
	this->FindCount = 1;
}

ItemData::ItemData(){}


//check
int ItemData::getFindCount(){
	return this->FindCount;
}

void ItemData::addFindCount()
{
this->FindCount++;
}

void ItemData::setFindCountZero()
{
this->FindCount = 0;
}