#include "kernelRW.h"

int has_upper = 0;
int has_lower = 0;
int has_symbol = 0;
int has_digit = 0;
int fd;
pid_t pid;
char *derive;

typedef struct _COPY_MEMORY
{
    pid_t pid;
    uintptr_t addr;
    void *buffer;
    size_t size;
} COPY_MEMORY, *PCOPY_MEMORY;

typedef struct _MODULE_BASE
{
    pid_t pid;
    char *name;
    uintptr_t base;
} MODULE_BASE, *PMODULE_BASE;

enum OPERATIONS
{
    OP_INIT_KEY = 0x800,
    OP_READ_MEM = 0x801,
    OP_WRITE_MEM = 0x802,
    OP_MODULE_BASE = 0x803,
};

int symbol_file(const char *filename)
{
    // 判断文件名是否含小写并且不含大写不含数字不含符号
    int length = strlen(filename);
    for (int i = 0; i < length; i++)
    {
        if (islower(filename[i]))
        {
            has_lower = 1;
        }
        else if (isupper(filename[i]))
        {
            has_upper = 1;
        }
        else if (ispunct(filename[i]))
        {
            has_symbol = 1;
        }
        else if (isdigit(filename[i]))
        {
            has_digit = 1;
        }
    }
    return has_lower && !has_upper && !has_symbol && !has_digit;
}

char *driver_path()
{
    // 打开目录
    const char *dev_path = "/dev";
    DIR *dir = opendir(dev_path);
    if (dir == NULL)
    {
        printf("无法打开/dev目录\n");
        return NULL;
    }

    struct dirent *entry;
    static char file_path[256];
    while ((entry = readdir(dir)) != NULL)
    {
        // 跳过当前目录和上级目录
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0)
        {
            continue;
        }
        sprintf(file_path, "%s/%s", dev_path, entry->d_name);
        // 获取文件stat结构
        struct stat file_info;
        if (stat(file_path, &file_info) < 0)
        {
            continue;
        }

        // 跳过gpio接口
        if (strstr(entry->d_name, "gpiochip") != NULL)
        {
            continue;
        }

        // 检查是否为驱动文件
        if ((S_ISCHR(file_info.st_mode) || S_ISBLK(file_info.st_mode)) && strchr(entry->d_name, '_') == NULL && strchr(entry->d_name, '-') == NULL && strchr(entry->d_name, ':') == NULL)
        {
            // 过滤标准输入输出
            if (strcmp(entry->d_name, "stdin") == 0 || strcmp(entry->d_name, "stdout") == 0 || strcmp(entry->d_name, "stderr") == 0)
            {
                continue;
            }

            size_t file_name_length = strlen(entry->d_name);
            time_t current_time;
            time(&current_time);
            int current_year = localtime(&current_time)->tm_year + 1900;
            int file_year = localtime(&file_info.st_ctime)->tm_year + 1900;
            // 跳过1980年前的文件
            if (file_year <= 1980)
            {
                continue;
            }

            time_t atime = file_info.st_atime;
            time_t ctime = file_info.st_ctime;
            // 检查最近访问时间和修改时间是否一致并且文件名是否是symbol文件
            if ((atime == ctime) /* && symbol_file(entry->d_name)*/)
            {
                // 检查mode权限类型是否为S_IFREG(普通文件)和大小还有gid和uid是否为0(root)并且文件名称长度在7位或7位以下
                if ((file_info.st_mode & S_IFMT) == 8192 && file_info.st_size == 0 && file_info.st_gid == 0 && file_info.st_uid == 0 && file_name_length <= 6)
                {
                    derive = file_path;
                    closedir(dir);
                    return file_path;
                }
            }
        }
    }
    closedir(dir);
    return NULL;
}

char *find_driver_path()
{
    // 打开目录
    const char *dev_path = "/dev";
    DIR *dir = opendir(dev_path);
    if (dir == NULL)
    {
        printf("无法打开/dev目录\n");
        return NULL;
    }

    char *files[] = {"wanbai", "CheckMe", "Ckanri", "lanran", "video188"};
    struct dirent *entry;
    char *file_path = NULL;
    while ((entry = readdir(dir)) != NULL)
    {
        // 跳过当前目录和上级目录
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0)
        {
            continue;
        }

        size_t path_length = strlen(dev_path) + strlen(entry->d_name) + 2;
        file_path = (char *)malloc(path_length);
        snprintf(file_path, path_length, "%s/%s", dev_path, entry->d_name);
        for (int i = 0; i < 5; i++)
        {
            if (strcmp(entry->d_name, files[i]) == 0)
            {
                closedir(dir);
                return file_path; // 移除原printf，统一在open_driver输出
            }
        }

        // 获取文件stat结构
        struct stat file_info;
        if (stat(file_path, &file_info) < 0)
        {
            free(file_path);
            file_path = NULL;
            continue;
        }

        // 跳过gpio接口
        if (strstr(entry->d_name, "gpiochip") != NULL)
        {
            free(file_path);
            file_path = NULL;
            continue;
        }

        // 检查是否为驱动文件
        if ((S_ISCHR(file_info.st_mode) || S_ISBLK(file_info.st_mode)) && strchr(entry->d_name, '_') == NULL && strchr(entry->d_name, '-') == NULL && strchr(entry->d_name, ':') == NULL)
        {
            // 过滤标准输入输出
            if (strcmp(entry->d_name, "stdin") == 0 || strcmp(entry->d_name, "stdout") == 0 || strcmp(entry->d_name, "stderr") == 0)
            {
                free(file_path);
                file_path = NULL;
                continue;
            }

            size_t file_name_length = strlen(entry->d_name);
            time_t current_time;
            time(&current_time);
            int current_year = localtime(&current_time)->tm_year + 1900;
            int file_year = localtime(&file_info.st_ctime)->tm_year + 1900;
            // 跳过1980年前的文件
            if (file_year <= 1980)
            {
                free(file_path);
                file_path = NULL;
                continue;
            }

            time_t atime = file_info.st_atime;
            time_t ctime = file_info.st_ctime;
            // 检查最近访问时间和修改时间是否一致并且文件名是否是symbol文件
            if ((atime == ctime) /* && symbol_file(entry->d_name)*/)
            {
                // 检查mode权限类型是否为S_IFREG(普通文件)和大小还有gid和uid是否为0(root)并且文件名称长度在7位或7位以下
                if ((file_info.st_mode & S_IFMT) == 8192 && file_info.st_size == 0 && file_info.st_gid == 0 && file_info.st_uid == 0 && file_name_length <= 9)
                {
                    closedir(dir);
                    return file_path; // 移除原printf，统一在open_driver输出
                }
            }
        }
        free(file_path);
        file_path = NULL;
    }
    closedir(dir);
    return NULL;
}

int open_driver(int isll)
{
    char *dev_path1 = NULL;
    if (isll == 1)
        dev_path1 = find_driver_path();
    else if (isll == 2)
        dev_path1 = driver_path();

    if (dev_path1 != NULL)
    {
        fd = open(dev_path1, O_RDWR);
        if (fd > 0)
        {
            // 统一输出绿色成功提示
            printf("\033[32m驱动读取成功：%s\033[0m\n", dev_path1);
            if (isll == 1)
                free(dev_path1);
            return 1;
        }
        if (isll == 1)
            free(dev_path1);
    }
    return 0;
}

kernelRW::kernelRW(int pid) : mainRW(pid)
{
    int isll = 1;

    /*printf("\033[92m驱动1:[动态分配内存malloc，手动释放，灵活性高注意泄漏]\033[0m\n");
    printf("\033[92m驱动2:[使用静态数组存储路径，无需释放，内存安全性更强]\033[0m\n");
    printf("请选择驱动读取方式(输入1是选择优先║输入2是筛选优先)\n");
  //  scanf("%d", &isll);
    if (!open_driver(isll))
    {
        printf("没有驱动文件-DEVPro驱动需每次退出重启\n");
        exit(0);
    }*/
    fd = open("/dev/YBYBBB", O_RDWR);
    if (fd <= 0)
    {
        printf("没有驱动文件-DEVPro驱动需每次退出重启\n");
        exit(0);
    }
    this->pid = pid;
}

bool kernelRW::readv(uintptr_t address, void *buffer, size_t size)
{
    COPY_MEMORY cm;
    cm.pid = this->pid;
    cm.addr = address;
    cm.buffer = buffer;
    cm.size = size;
    if (ioctl(fd, OP_READ_MEM, &cm) != 0)
    {
        return false;
    }
    return true;
}

bool kernelRW::writev(uintptr_t address, void *buffer, size_t size)
{
    COPY_MEMORY cm;
    cm.pid = this->pid;
    cm.addr = address;
    cm.buffer = buffer;
    cm.size = size;
    if (ioctl(fd, OP_WRITE_MEM, &cm) != 0)
    {
        return false;
    }
    return true;
}