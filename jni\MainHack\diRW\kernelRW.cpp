#include "kernelRW.h"









kernelRW::kernelRW(int pid) : mainRW(pid)
{
    // 创建Driver实例
    driver = new Driver();

    // 初始化pid
    if (!driver->initpid(pid))
    {
        printf("内核驱动初始化失败\n");
        delete driver;
        driver = nullptr;
        exit(0);
    }

    printf("\033[32m内核驱动初始化成功\033[0m\n");
}

kernelRW::~kernelRW()
{
    if (driver != nullptr)
    {
        delete driver;
        driver = nullptr;
    }
}

bool kernelRW::readv(uintptr_t address, void *buffer, size_t size)
{
    if (driver == nullptr)
    {
        return false;
    }

    // 使用Driver类的read方法进行内核读取
    return driver->read(address, buffer, size);
}

bool kernelRW::writev(uintptr_t address, void *buffer, size_t size)
{
    if (driver == nullptr)
    {
        return false;
    }

    // 使用Driver类的write方法进行内核写入
    return driver->write(address, buffer, size);
}